@import "./tailwind.css";
@import "./base.css";
@import "./typography.css";
@import "./button.css";
@import "./divider.css";
@import "./table.css";
@import "./tag.css";
@import "./badge.css";
@import "filepond/dist/filepond.min.css";

/* Style */

.v-popper--theme-light .v-popper__inner {
    background: #ffffff;
    padding: 8px 12px;
    border: 1.5px #dcdddd solid;
    border-radius: 4px;
    max-width: 100px;
    @apply max-w-sm;
}

.v-popper--theme-light .v-popper__arrow-outer {
    border-color: #dcdddd;
}

.v-popper--theme-light .v-popper__arrow-inner {
    visibility: visible;
    border-color: #ffffff;
}

/* Vue Datepicker theming */
.dp__theme_light,
.dp__theme_dark {
    --dp-primary-color: #f13997 !important;
    --dp-menu-border-color: transparent !important;
    --dp-hover-color: #ffedf6 !important;

    --dp-background-color: #212121;
    --dp-text-color: #fff;
    --dp-hover-text-color: #fff;
    --dp-hover-icon-color: #959595;
    --dp-primary-disabled-color: #61a8ea;
    --dp-primary-text-color: #fff;
    --dp-secondary-color: #a9a9a9;
    --dp-border-color-hover: #aaaeb7;
    --dp-border-color-focus: #aaaeb7;
    --dp-disabled-color: #737373;
    --dp-disabled-color-text: #d0d0d0;
    --dp-scroll-bar-background: #212121;
    --dp-scroll-bar-color: #484848;
    --dp-success-color: #00701a;
    --dp-success-color-disabled: #428f59;
    --dp-icon-color: #959595;
    --dp-danger-color: #e53935;
    --dp-marker-color: #e53935;
    --dp-tooltip-color: #3e3e3e;
    --dp-highlight-color: rgb(0 92 178 / 20%);
    --dp-range-between-dates-background-color: var(--dp-hover-color, #484848);
    --dp-range-between-dates-text-color: var(--dp-hover-text-color, #fff);
    --dp-range-between-border-color: var(--dp-hover-color, #fff);
}

.dp__menu_inner.dp__flex_display {
    gap: 4rem;
}

:root {
    --dp-action-buttons-padding: 20px 44px !important; /*Adjust padding for the action buttons in action row*/

    --dp-menu-padding: 16px !important; /*Menu padding*/
    --dp-action-row-padding: 16px !important;

    /*Font sizes*/
    --dp-font-size: 1rem; /*Default font-size*/
    --dp-preview-font-size: 1rem !important; /*Font size of the date preview in the action row*/

    /* -------------- */

    /*General*/
    --dp-font-family: "Open Sans", sans-serif;
    --dp-border-radius: 4px; /*Configurable border-radius*/
    --dp-cell-border-radius: 16px !important; /*Specific border radius for the calendar cell*/
    --dp-common-transition: all 0.1s ease-in; /*Generic transition applied on buttons and calendar cells*/

    /*Sizing*/
    --dp-button-height: 35px; /*Size for buttons in overlays*/
    --dp-month-year-row-height: 35px; /*Height of the month-year select row*/
    --dp-month-year-row-button-size: 35px; /*Specific height for the next/previous buttons*/
    --dp-button-icon-height: 20px; /*Icon sizing in buttons*/
    --dp-cell-size: 48px !important; /*Width and height of calendar cell*/
    --dp-cell-padding: 5px; /*Padding in the cell*/
    --dp-common-padding: 10px; /*Common padding used*/
    --dp-input-icon-padding: 35px; /*Padding on the left side of the input if icon is present*/
    --dp-input-padding: 6px 30px 6px 12px; /*Padding in the input*/
    --dp-menu-min-width: 260px; /*Adjust the min width of the menu*/

    --dp-row-margin: 5px 0; /*Adjust the spacing between rows in the calendar*/
    --dp-calendar-header-cell-padding: 0.5rem; /*Adjust padding in calendar header cells*/
    --dp-two-calendars-spacing: 10px; /*Space between multiple calendars*/
    --dp-overlay-col-padding: 3px; /*Padding in the overlay column*/
    --dp-time-inc-dec-button-size: 32px; /*Sizing for arrow buttons in the time picker*/
}
