<template>

    <Head title="Log in">
        <meta name="description" content="Login to Pravi." />
    </Head>

    <AuthLayout>
        <div class="w-full max-w-[70%] md:px-24 lg:px-48">
            <div class="text-center">
                <h1 class="fs-2xl mb-8 font-bold">Welcome back</h1>
                <p>Log in to create or manage a campaign</p>
            </div>

            <form @submit.prevent="submit" class="my-40">
                <TextInput id="email" v-model="form.email" name="email" label="Email address" type="email" required
                    :serverError="form.errors.email"></TextInput>

                <TextInput id="password" v-model="form.password" name="password" label="Password" type="password"
                    required :serverError="form.errors.password"></TextInput>

                <div class="flex flex-col space-y-32 items-center justify-between">
                    <Link v-if="canResetPassword" :href="route('password.request')"
                        class="self-end text-surface-action underline">
                    Forgot your password?
                    </Link>

                    <Button type="submit" class="h-48 w-full" color="action" size="md" :disabled="form.processing">
                        Log In
                    </Button>

                    <div class="flex flex-col space-y-8 w-full">
                        <Button class="w-full h-48" :icon-left="GoogleIcon">
                            <a class="h-full w-full" href="/auth/google/redirect">
                                Continue with Google
                            </a>
                        </Button>
                        <Button class="w-full h-48" :icon-left="WindowsIcon">
                            <a class="h-full w-full" href="/auth/google/redirect">
                                Continue with Google
                            </a>
                        </Button>
                    </div>

                    <div class="mt-40 w-full border-t border-border-primary-light pt-40 text-center">
                        <p>
                            Don't have an account?

                            <Link :href="route('register')" class="text-surface-action underline">
                            Sign up
                            </Link>
                        </p>
                    </div>
                </div>
            </form>
        </div>
    </AuthLayout>
</template>

<script setup>
import { Head, Link, useForm } from "@inertiajs/vue3";
import TextInput from "@/Components/TextInput/TextInput.vue";
import Button from "@/Components/Button/Button.vue";
import AuthLayout from "@/Layouts/AuthLayout.vue";
import GoogleIcon from "./GoogleIcon.vue";
import WindowsIcon from "./WindowsIcon.vue";

defineProps({
    canResetPassword: Boolean,
    status: String,
});

const form = useForm({
    email: "",
    password: "",
});

const submit = () => {
    form.post(route("login"), {
        onFinish: () => form.reset("password"),
    });
};
</script>
