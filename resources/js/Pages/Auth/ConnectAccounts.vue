<template>
    <Head title="Connect your accounts">
        <meta
            name="description"
            content="<PERSON>ra<PERSON> works best when it is connected to your other applications. For live benchmarking, campaign creation and predictive insights tick which accounts you have."
        />
    </Head>

    <AuthLayout>
        <template v-slot:aside>
            <AuthAside :activePage="5"></AuthAside>
        </template>

        <div class="w-full max-w-[844px] md:px-24 lg:px-48">
            <h1 class="fs-2xl mb-16 font-bold">Connect your accounts</h1>

            <p class="mb-8">
                Pravi works best when it is connected to your other
                applications.
            </p>

            <template v-if="auth.user.is_company_owner">
                <p class="mb-40">
                    For live benchmarking, campaign creation and predictive
                    insights tick which accounts you have below
                </p>

                <div class="grid gap-24 xl:grid-cols-2">
                    <IntegrationCard
                        v-for="service in integrations"
                        :key="service.name"
                        :service="service"
                        :isConnected="company[service.integrationCheck]"
                    />
                </div>

                <p class="my-40">
                    <a
                        class="font-medium text-surface-action"
                        href="/setup/connect-accounts"
                        >Refresh this page</a
                    >
                    after connecting an account to update connection status.
                </p>

                <div class="my-40 flex justify-end gap-32">
                    <Link
                        href="/setup/add-users"
                        class="btn btn--default btn--md h-[48px] min-w-[110px]"
                        >Back</Link
                    >

                    <Link
                        href="/setup/thank-you"
                        class="btn btn--default btn--md h-[48px] min-w-[110px]"
                        >Continue</Link
                    >
                </div>
            </template>

            <template v-else>
                <p class="mb-40">
                    Only the organisation admin can modify integration settings.
                    Please reach out to the owner of your organisation's admin
                    Pravi account for assistance.
                </p>
            </template>
        </div>
    </AuthLayout>
</template>

<script setup>
import { Head, Link } from "@inertiajs/vue3";
import AuthLayout from "@/Layouts/AuthLayout.vue";
import AuthAside from "@/Components/AuthAside/AuthAside.vue";

import IntegrationCard from "@/Components/IntegrationRow/IntegrationCard.vue";

import { useIntegrationsStore } from "@/stores/integrations";

const { integrations } = useIntegrationsStore();

const { auth, company } = defineProps({ auth: Object, company: Object });
</script>
