<template>
    <Head title="Thank you">
        <meta name="description" content="Thank you for signing up to <PERSON>ravi." />
    </Head>

    <AuthLayout>
        <template v-slot:aside>
            <AuthAside></AuthAside>
        </template>

        <div
            class="w-full max-w-[844px] text-center md:px-24 md:text-left lg:px-48"
        >
            <h1 class="fs-5xl font-bold">Thank you</h1>

            <h2 class="fs-2xl my-40 font-bold">Your sign up is complete</h2>

            <ButtonLink
                href="/build/campaign"
                color="action"
                class="mb-200 h-48"
                >Continue to Dashboard</ButtonLink
            >

            <div class="border-t border-t-border-primary-light py-24">
                <Link
                    class="cursor-pointer text-text-action-hover underline"
                    href="/contact-us"
                >
                    Do you need help? Contact us.
                </Link>
            </div>
        </div>
    </AuthLayout>
</template>

<script setup>
import { Head, useForm, <PERSON> } from "@inertiajs/vue3";
import AuthLayout from "@/Layouts/AuthLayout.vue";
import AuthAside from "@/Components/AuthAside/AuthAside.vue";
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";

defineProps({
    canResetPassword: Boolean,
    status: String,
});

const form = useForm({
    email: "",
    password: "",
});

const submit = () => {
    form.post(route("login"), {
        onFinish: () => form.reset("password"),
    });
};
</script>
