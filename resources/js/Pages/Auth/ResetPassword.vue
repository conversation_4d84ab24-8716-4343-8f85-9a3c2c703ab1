<template>
    <Head title="Reset password">
        <meta name="description" content="Reset your Pravi password." />
    </Head>

    <AuthLayout>
        <div class="w-full max-w-[844px] md:px-24 lg:px-48">
            <div class="text-center">
                <h1 class="fs-2xl mb-8 font-bold">Reset password</h1>
                <p>Make sure it's something you'll remember</p>
            </div>

            <form @submit.prevent="submit" class="my-40">
                <TextInput
                    id="email"
                    v-model="form.email"
                    name="email"
                    label="Email address"
                    type="email"
                    required
                    :serverError="form.errors.email"
                ></TextInput>

                <TextInput
                    id="password"
                    v-model="form.password"
                    name="password"
                    label="Password"
                    type="password"
                    required
                    :rules="[rules.required, rules.password]"
                    :serverError="form.errors.password"
                ></TextInput>

                <TextInput
                    id="password_confirmation"
                    v-model="form.password_confirmation"
                    name="password_confirmation"
                    label="Confirm Password"
                    type="password"
                    required
                    :rules="[rules.required, rules.password]"
                    :serverError="form.errors.password_confirmation"
                ></TextInput>

                <div class="mb-16 min-h-[88px]">
                    <ul v-show="password" class="fs-xs mb-32 space-y-4">
                        <li class="flex items-center gap-8">
                            <component
                                :is="
                                    isMinLength ? IconCircleCheck : IconCircleX
                                "
                            />
                            At least 8 characters
                        </li>
                        <li class="flex items-center gap-8">
                            <component
                                :is="hasNumber ? IconCircleCheck : IconCircleX"
                            />
                            At least one number
                        </li>
                        <li class="flex items-center gap-8">
                            <component
                                :is="
                                    hasUpperAndLower
                                        ? IconCircleCheck
                                        : IconCircleX
                                "
                            />
                            Upper and lower case
                        </li>
                    </ul>
                </div>

                <div
                    class="mt-4 flex flex-col items-center justify-between gap-32 md:flex-row"
                >
                    <Button
                        type="submit"
                        class="order-2 h-[48px] w-full"
                        color="action"
                        size="md"
                        :disabled="form.processing"
                    >
                        Save new password
                    </Button>
                </div>
            </form>
        </div>
    </AuthLayout>
</template>

<script setup>
import { Head, useForm } from "@inertiajs/vue3";

import TextInput from "@/Components/TextInput/TextInput.vue";
import Button from "@/Components/Button/Button.vue";
import AuthLayout from "@/Layouts/AuthLayout.vue";

import rules from "@/utilities/validation-rules";
import { computed } from "vue";
import IconCircleCheck from "@/Components/Icons/IconCircleCheck.vue";
import IconCircleX from "@/Components/Icons/IconCircleX.vue";

const props = defineProps({
    email: String,
    token: String,
});

const form = useForm({
    token: props.token,
    email: props.email,
    password: "",
    password_confirmation: "",
});

const password = computed(() => form.password);

const isMinLength = computed(() => password.value.length >= 8);
const hasNumber = computed(() => /\d/.test(password.value));
const hasUpperAndLower = computed(
    () => /[a-z]/.test(password.value) && /[A-Z]/.test(password.value),
);

const submit = () => {
    form.post(route("password.update"), {
        onFinish: () => form.reset("password", "password_confirmation"),
    });
};
</script>
