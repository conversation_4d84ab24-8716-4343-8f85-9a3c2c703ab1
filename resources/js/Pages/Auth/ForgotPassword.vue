<template>
    <Head title="Forgot password" />

    <AuthLayout>
        <div class="w-full max-w-[844px] md:px-24 lg:px-48">
            <template v-if="!resetLinkSent">
                <div class="text-center">
                    <h1 class="fs-2xl mb-8 font-bold">Forgot password?</h1>
                    <p>
                        Enter your email, and we'll send you a link to reset it
                    </p>
                </div>

                <div v-if="status" class="my-4 font-medium text-success-base">
                    {{ status }}
                </div>

                <form @submit.prevent="submit" class="my-40">
                    <TextInput
                        id="email"
                        v-model="form.email"
                        name="email"
                        label="Email address"
                        type="email"
                        required
                        :serverError="form.errors.email"
                    ></TextInput>

                    <div
                        class="mt-4 flex flex-col items-center justify-between gap-32 md:flex-row"
                    >
                        <Button
                            type="submit"
                            class="order-2 h-[48px] w-full"
                            color="action"
                            size="md"
                            :disabled="form.processing"
                        >
                            Send Reset Link
                        </Button>
                    </div>
                </form>
            </template>

            <template v-else>
                <div class="text-center">
                    <h1 class="fs-2xl mb-24 font-bold">All done!</h1>
                    <p class="mb-12">
                        A password reset link has been sent to <br />your email
                        address.
                    </p>
                    <p>
                        Remember to check your Spam folder if <br />you can't
                        find it.
                    </p>
                </div>
            </template>
        </div>
    </AuthLayout>
</template>

<script setup>
import { Head, useForm } from "@inertiajs/vue3";

import TextInput from "@/Components/TextInput/TextInput.vue";
import Button from "@/Components/Button/Button.vue";
import AuthLayout from "@/Layouts/AuthLayout.vue";
import { ref } from "vue";

const resetLinkSent = ref(false);

defineProps({
    status: String,
});

const form = useForm({
    email: "",
});

const submit = () => {
    form.post(route("password.email"), {
        onSuccess: () => {
            resetLinkSent.value = true;
        },
    });
};
</script>
