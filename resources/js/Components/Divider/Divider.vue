<template>
    <div class="divider" :class="sizeClass">
        <div class="divider__line"></div>
        <span class="divider__text">{{ text }}</span>
        <div class="divider__line"></div>
    </div>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
    text: {
        type: String,
        default: "or",
    },
    size: {
        type: String,
        default: "md",
        validator: (value) => {
            return ["md", "lg", "xl"].includes(value);
        },
    },
});

const sizeClass = computed(() => {
    return {
        "divider--md": props.size === "md",
        "divider--lg": props.size === "lg",
        "divider--xl": props.size === "xl",
    };
});
</script>
