<template>
    <div class="flex items-center">
        <!-- First circle -->
        <div
            class="relative z-10 flex size-32 items-center justify-center rounded-full bg-surface-action text-white md:size-48"
            :class="
                isStepReached(1)
                    ? 'bg-surface-action'
                    : 'bg-surface-action-light'
            "
        >
            1
        </div>

        <!-- Remaining segments and circles -->
        <template v-for="(count, index) in steps.slice(1)" :key="index">
            <!-- Segment between step index+1 and index+2 -->
            <div
                class="relative h-8 flex-1 scale-x-105 bg-surface-grey-light md:h-12"
                :class="steps[index] > 1 ? 'grow-[1.7]' : ''"
            >
                <div
                    class="absolute h-8 rounded-2xl bg-surface-action md:h-12"
                    :style="{ width: segmentFillWidth(index + 1) + '%' }"
                ></div>
            </div>
            <!-- Circle -->
            <div
                class="relative z-10 flex size-32 items-center justify-center rounded-full text-white md:size-48"
                :class="
                    isStepReached(index + 2)
                        ? 'bg-surface-action'
                        : 'bg-surface-action-light'
                "
            >
                {{ index + 2 }}
            </div>
        </template>
    </div>
</template>

<script>
export default {
    name: "Stepper",
    props: {
        /**
         * Array of sub-step counts per main step
         */
        steps: {
            type: Array,
            required: true,
            validator(v) {
                return (
                    Array.isArray(v) &&
                    v.every((n) => typeof n === "number" && n > 0)
                );
            },
        },
        /**
         * Current position: which main step and which sub-step within it
         */
        currentStep: {
            type: Object,
            required: true,
            validator(o) {
                // Only basic shape validation here, cross-props checks done in usage
                return (
                    o && typeof o.step === "number" && typeof o.sub === "number"
                );
            },
        },
    },
    methods: {
        /**
         * Percent fill of segment after main step i
         */
        segmentFillWidth(index) {
            const { step, sub } = this.currentStep;
            const totalSteps = this.steps.length;
            const s = Math.min(Math.max(step, 1), totalSteps); // Clamp step
            const maxSub = this.steps[index - 1] || 1; // How many substeps at this segment

            if (s > index) {
                // If we're past this segment, it's fully filled
                return 100;
            }

            if (s < index) {
                // If we haven't reached this segment yet, it's empty
                return 0;
            }

            // We are *at* this segment:
            if (maxSub === 1) {
                // If this step has only 1 substep, don't fill it partially — keep at 0
                return 0;
            }

            const sb = Math.min(Math.max(sub, 1), maxSub);
            return Math.round((sb / maxSub) * 100) - 25;
        },

        isStepReached(index) {
            const { step } = this.currentStep;
            if (index <= step) return true;
        },
    },
};
</script>
