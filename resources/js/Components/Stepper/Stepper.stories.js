import Stepper from "./Stepper.vue";

export default {
    component: Stepper,
    tags: ["autodocs"],
    // argTypes: {
    //     steps: { control: "object" },
    //     currentStep: { control: "object" },
    // },
};

export const Default = {
    args: {
        // 4 main steps: first has 4 sub-steps, the rest have 1 each
        steps: [4, 1, 1, 1],
        // starting at step 2, sub-step 1 (you can update these in Storybook controls)
        currentStep: { step: 2, sub: 1 },
    },
};
