<template>
    <div class="flex min-h-screen flex-col">
        <!-- Page Heading -->
        <header class="border-b border-border-primary-light p-16">
            <img
                src="../../images/pravi-logo-text.png"
                alt="Pravi"
                width="129"
                height="36"
                class="mx-auto"
            />
        </header>

        <!-- Page Content -->
        <main
            class="flex flex-1 items-stretch justify-center gap-48 xl:gap-[130px]"
        >
            <div
                class="mx-auto my-48 w-full max-w-2xl flex-1 px-24 lg:my-104 lg:mr-0 lg:pr-0"
            >
                <img
                    class="mb-88"
                    src="/images/logo.png"
                    height="68px"
                    width="60px"
                    alt=""
                />

                <Stepper
                    v-if="currentStep"
                    :steps="[1, 1, 1, 1, 1, 1]"
                    :currentStep="currentStep"
                    class="mb-48"
                />

                <slot />
            </div>

            <div
                class="relative hidden w-2/5 bg-surface-information-light lg:flex lg:items-end lg:justify-start"
            >
                <slot name="image"></slot>
            </div>
        </main>

        <!-- Page Footer -->
        <Footer :navLinks="footerLinks" />

        <CookieConsent />
    </div>
</template>

<script setup>
import Footer from "@/Components/Footer/Footer.vue";
import CookieConsent from "@/Components/CookieConsent/CookieConsent.vue";
import Stepper from "@/Components/Stepper/Stepper.vue";

defineProps({
    title: String,
    currentStep: Object,
});

const footerLinks = [
    { text: "Privacy policy", url: "/privacy-policy" },
    { text: "Terms and conditions", url: "/terms-of-service" },
];
</script>
