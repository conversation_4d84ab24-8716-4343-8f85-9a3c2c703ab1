<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Icon Test</title>
    <style>
        /* Simulate the button styles for testing */
        .btn {
            display: inline-flex;
            cursor: pointer;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            border: 1px solid #ccc;
            padding: 8px 16px;
            background: white;
            color: #333;
            font-size: 14px;
            margin: 8px;
        }
        
        .btn__icon {
            flex-shrink: 0;
            width: 16px;
            height: 16px;
        }
        
        .btn__icon--left {
            margin-right: 12px;
        }
        
        .btn__icon--right {
            margin-left: 12px;
        }
        
        .test-container {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .test-section h3 {
            margin-bottom: 10px;
            color: #333;
        }
        
        /* Icon placeholder */
        .icon {
            width: 16px;
            height: 16px;
            background: #666;
            border-radius: 2px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Button Icon Positioning Test</h1>
        
        <div class="test-section">
            <h3>Left Icon + Text (12px spacing)</h3>
            <button class="btn">
                <span style="display: flex; align-items: center;">
                    <span class="btn__icon btn__icon--left icon"></span>
                    <span>Button Text</span>
                </span>
            </button>
            
            <button class="btn">
                <span style="display: flex; align-items: center;">
                    <span class="btn__icon btn__icon--left icon"></span>
                    <span>Longer Button Text</span>
                </span>
            </button>
        </div>
        
        <div class="test-section">
            <h3>Text + Right Icon (12px spacing)</h3>
            <button class="btn">
                <span style="display: flex; align-items: center;">
                    <span>Button Text</span>
                    <span class="btn__icon btn__icon--right icon"></span>
                </span>
            </button>
            
            <button class="btn">
                <span style="display: flex; align-items: center;">
                    <span>Longer Button Text</span>
                    <span class="btn__icon btn__icon--right icon"></span>
                </span>
            </button>
        </div>
        
        <div class="test-section">
            <h3>Both Icons + Text (12px spacing each)</h3>
            <button class="btn">
                <span style="display: flex; align-items: center;">
                    <span class="btn__icon btn__icon--left icon"></span>
                    <span>Button Text</span>
                    <span class="btn__icon btn__icon--right icon"></span>
                </span>
            </button>
            
            <button class="btn">
                <span style="display: flex; align-items: center;">
                    <span class="btn__icon btn__icon--left icon"></span>
                    <span>Longer Button Text</span>
                    <span class="btn__icon btn__icon--right icon"></span>
                </span>
            </button>
        </div>
        
        <div class="test-section">
            <h3>Icon Only</h3>
            <button class="btn">
                <span style="display: flex; align-items: center;">
                    <span class="btn__icon icon"></span>
                </span>
            </button>
        </div>
        
        <div class="test-section">
            <h3>Fixed Width Buttons (showing centered grouping)</h3>
            <button class="btn" style="width: 200px;">
                <span style="display: flex; align-items: center;">
                    <span class="btn__icon btn__icon--left icon"></span>
                    <span>Short</span>
                </span>
            </button>
            
            <button class="btn" style="width: 200px;">
                <span style="display: flex; align-items: center;">
                    <span class="btn__icon btn__icon--left icon"></span>
                    <span>Medium Text</span>
                </span>
            </button>
            
            <button class="btn" style="width: 200px;">
                <span style="display: flex; align-items: center;">
                    <span class="btn__icon btn__icon--left icon"></span>
                    <span>Very Long Button Text</span>
                </span>
            </button>
        </div>
        
        <p><strong>Expected behavior:</strong> Icons should be positioned adjacent to text with 12px spacing, and the icon+text group should be centered within the button container.</p>
    </div>
</body>
</html>
